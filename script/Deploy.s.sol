// SPDX-License-Identifier: MIT
pragma solidity ^0.8.13;

import "forge-std/Script.sol";
import "../src/Token.sol";

contract DeployScript is Script {
    function run() external {
        uint256 deployerPrivateKey = vm.envUint("PRIVATE_KEY");
        address recipient = vm.envAddress("RECIPIENT");

        vm.startBroadcast(deployerPrivateKey);

        Token token = new Token();

        // Transfer all tokens to the recipient
        uint256 totalSupply = token.totalSupply();
        token.transfer(recipient, totalSupply);

        vm.stopBroadcast();
    }
}