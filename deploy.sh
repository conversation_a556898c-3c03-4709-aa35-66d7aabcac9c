#!/bin/bash

# Load environment variables from .env file
source .env

# Check if PRIVATE_KEY is set
if [ -z "$PRIVATE_KEY" ]; then
    echo "Error: PRIVATE_KEY not found in .env file"
    echo "Please copy .env.example to .env and add your private key"
    exit 1
fi

RPC_URL="https://base.drpc.org"

echo "Deploying Token contract..."
echo "RPC URL: $RPC_URL"

# Run the deployment script
forge script script/Deploy.s.sol --rpc-url "$RPC_URL" --broadcast --verify

echo "Deployment complete!"